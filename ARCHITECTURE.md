# Архитектурная схема системы доставки рыночных данных

## 🏗️ Общая архитектура

```
┌─────────────────────────────────────────────────────────────────┐
│                        Frontend Layer                          │
├─────────────────────────────────────────────────────────────────┤
│  Next.js + React 18 + TanStack Query + Lightweight Charts     │
│  ┌───────────────┐ ┌───────────────┐ ┌───────────────────────┐ │
│  │ Real-time UI  │ │ Table Virtual │ │ Chart Components      │ │
│  │ WebSocket     │ │ TanStack      │ │ Lightweight Charts v5 │ │
│  │ Subscriptions │ │ Table         │ │ Multi-timeframe       │ │
│  └───────────────┘ └───────────────┘ └───────────────────────┘ │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │               TanStack Query Cache Layer                   │ │
│  │  • Intelligent caching • Background updates              │ │
│  │  • Optimistic updates • Error boundaries                 │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                    │
                             ┌──────┴──────┐
                             │ WebSocket + │
                             │ REST API    │
                             └──────┬──────┘
┌─────────────────────────────────────────────────────────────────┐
│                     Backend Layer (Elysia/Bun)                 │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────────┐ │
│  │ WebSocket       │ │ REST API        │ │ Pub/Sub Manager     │ │
│  │ Manager         │ │ Endpoints       │ │ Redis Dragonfly     │ │
│  │ • Connection    │ │ • OHLCV data    │ │ • Real-time events  │ │
│  │   pooling       │ │ • Tickers       │ │ • User subscriptions│ │
│  │ • Room-based    │ │ • Symbols       │ │ • Batch updates     │ │
│  │   subscriptions │ │ • Historical    │ │ • Rate limiting     │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────────┘ │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                Data Processing Layer                        │ │
│  │  • Market data aggregation • Indicator calculations        │ │
│  │  • Volume analysis         • Price alerts                  │ │
│  │  • Trade filtering         • Batch processing              │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                    │
┌─────────────────────────────────────────────────────────────────┐
│                      Data Layer                                │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────────┐ │
│  │ Redis Dragonfly │ │ QuestDB         │ │ Exchange APIs       │ │
│  │ • L1 Cache      │ │ • Historical    │ │ • Binance WS/REST   │ │
│  │ • Pub/Sub       │ │   OHLCV data    │ │ • Real-time feeds   │ │
│  │ • Sessions      │ │ • Up to 5k      │ │ • Rate limit        │ │
│  │ • Rate limits   │ │   candles/pair  │ │   management        │ │
│  │ • Real-time     │ │ • Indicators    │ │ • Connection pools  │ │
│  │   data          │ │ • Aggregations  │ │ • Auto-reconnect    │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 🚀 Ключевые компоненты для реализации

### 1. Data Pipeline Architecture

#### 1.1 WebSocket Connection Manager
```typescript
// src/server/websocket/connectionManager.ts
class WebSocketConnectionManager {
  // Connection pooling для 10k+ users
  // Room-based subscriptions
  // Automatic load balancing
  // Graceful disconnection handling
}
```

#### 1.2 Redis Pub/Sub System
```typescript
// src/server/pubsub/redisManager.ts
class RedisPubSubManager {
  // Multi-channel subscriptions
  // Batch message processing
  // Message deduplication
  // Failover mechanisms
}
```

#### 1.3 QuestDB Integration
```typescript
// src/server/database/questdb.ts
class QuestDBService {
  // Optimized OHLCV queries
  // Time-series partitioning
  // Parallel data loading
  // Connection pooling
}
```

### 2. Frontend Optimization

#### 2.1 Enhanced TanStack Query Configuration
```typescript
// src/shared/lib/queryClient.ts
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 10, // 10 seconds
      cacheTime: 1000 * 60 * 5, // 5 minutes
      refetchOnWindowFocus: false,
      retry: (failureCount, error) => {
        // Smart retry logic
      }
    }
  }
});
```

#### 2.2 WebSocket Hook для Real-time Updates
```typescript
// src/shared/hooks/useWebSocket.ts
export function useWebSocket() {
  // Auto-reconnection
  // Message queuing
  // Connection state management
  // Subscription management
}
```

#### 2.3 Virtual Table для больших датасетов
```typescript
// src/features/TickerManagement/VirtualTable.tsx
export function VirtualTickerTable() {
  // TanStack Virtual integration
  // Dynamic row heights
  // Smooth scrolling
  // Memory optimization
}
```

### 3. Performance Optimizations

#### 3.1 Multi-level Caching Strategy
```
Browser Cache (TanStack Query) → Redis Cache → QuestDB
     ↓                              ↓            ↓
  5-30 seconds                  1-5 minutes   Persistent
```

#### 3.2 Batch Update System
```typescript
// src/server/services/batchProcessor.ts
class BatchUpdateProcessor {
  // Accumulate updates over 100-500ms
  // Send bulk updates to reduce WebSocket traffic
  // Priority-based message handling
}
```

#### 3.3 Connection Optimization
```typescript
// src/server/websocket/connectionOptimizer.ts
class ConnectionOptimizer {
  // Connection pooling
  // Load balancing across multiple instances
  // Geographic distribution
  // Health monitoring
}
```

## 📊 Масштабирование для 10k+ пользователей

### Horizontal Scaling Strategy

1. **Load Balancer** - распределение WebSocket connections
2. **Multiple Backend Instances** - Elysia/Bun workers
3. **Redis Cluster** - для Pub/Sub и кеширования
4. **QuestDB Replicas** - read replicas для исторических данных

### Performance Targets

- **Latency**: < 100ms для real-time updates
- **Throughput**: 100k+ messages/second
- **Memory**: < 512MB per 1k connections
- **CPU**: < 80% utilization at peak

### Monitoring & Observability

```typescript
// src/server/monitoring/metrics.ts
export class MetricsCollector {
  // WebSocket connection count
  // Message throughput
  // Cache hit rates
  // Database query performance
  // Memory and CPU usage
}
```

## 🔧 Приоритет реализации

### Phase 1: Core Infrastructure (Weeks 1-2)
1. WebSocket Connection Manager
2. Redis Pub/Sub integration
3. QuestDB schema and basic queries
4. Enhanced TanStack Query setup

### Phase 2: Real-time Features (Weeks 3-4)
1. Live ticker updates
2. Real-time chart data
3. WebSocket subscription management
4. Batch update system

### Phase 3: Performance Optimization (Weeks 5-6)
1. Virtual table implementation
2. Advanced caching strategies
3. Connection optimization
4. Load testing and tuning

### Phase 4: Scale Testing (Week 7)
1. Load testing with 10k+ connections
2. Performance monitoring setup
3. Horizontal scaling validation
4. Optimization based on metrics

## 💾 Database Schema (QuestDB)

```sql
-- OHLCV data table
CREATE TABLE ohlcv_data (
    symbol SYMBOL,
    interval SYMBOL,
    timestamp TIMESTAMP,
    open DOUBLE,
    high DOUBLE,
    low DOUBLE,
    close DOUBLE,
    volume DOUBLE,
    trades LONG,
    market_type SYMBOL
) timestamp(timestamp) PARTITION BY DAY;

-- Real-time tickers
CREATE TABLE tickers (
    symbol SYMBOL,
    price DOUBLE,
    change_24h DOUBLE,
    volume_24h DOUBLE,
    market_type SYMBOL,
    last_update TIMESTAMP
) timestamp(last_update);
```

## 🔄 Message Flow Architecture

```
Exchange API → Data Processor → Redis Pub/Sub → WebSocket Manager → Client
     ↓              ↓               ↓              ↓              ↓
   Raw data    Normalized data   Cached data   Filtered data   UI Updates
```

Эта архитектура обеспечит высокую производительность, масштабируемость и надежность для конкурентоспособной системы доставки рыночных данных.
