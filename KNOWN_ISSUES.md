# Известные проблемы проекта Metacharts

## Проблемы с типизацией Drizzle ORM

В проекте обнаружены проблемы с типизацией при использовании Drizzle ORM для построения сложных запросов. 
Проявляются следующие ошибки:

```
Type 'Omit<PgSelectBase<...>, ...>' is missing the following properties from type 'PgSelectBase<...>': config, joinsNotNullableMap, tableName, isPartialSelect, and 6 more.
```

```
Argument of type '(() => SQL<unknown>) | (() => boolean) | { readonly brand: "Table"; ... }' is not assignable to parameter of type 'SQLWrapper | AnyColumn'.
Type '() => SQL<unknown>' is not assignable to type 'SQLWrapper | AnyColumn'.
```

### Временное решение

Для обхода этой проблемы в файле `src/server/services/tickerService.ts` была реализована альтернативная стратегия:
1. Загрузка всех данных из таблицы
2. Фильтрация и сортировка данных в памяти
3. Возврат отфильтрованного и отсортированного результата

### Долгосрочное решение

Данный подход работает для небольших объемов данных на этапе разработки, 
но не подходит для промышленной эксплуатации с большими объемами данных.

Необходимые шаги для решения проблемы:
1. Обновить зависимости Drizzle ORM до последней версии
2. Проверить совместимость типов с PostgreSQL драйвером
3. Реализовать правильную типизацию для сложных запросов
4. Вернуть логику фильтрации и сортировки на уровень базы данных

## TODO: Оптимизация запросов к базе данных

В текущей версии есть следующие места, требующие оптимизации:
1. Метод `getTickers` в `tickerService.ts` - заменить фильтрацию в памяти на SQL-запросы
2. Создать индексы для часто используемых полей (symbol, marketType, quoteVolume)
3. Реализовать пагинацию на уровне базы данных 