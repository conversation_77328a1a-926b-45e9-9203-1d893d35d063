// This file will be populated by drizzle-kit introspect 

import { pgTable, serial, varchar, timestamp, decimal, index, unique, integer } from 'drizzle-orm/pg-core';

export const candles = pgTable('candles', {
  id: serial('id').primaryKey(),
  symbol: varchar('symbol', { length: 32 }).notNull(),
  interval: varchar('interval', { length: 16 }).notNull(),
  marketType: varchar('market_type', { length: 32 }).notNull(),
  openTime: timestamp('open_time', { withTimezone: true }).notNull(),
  open: decimal('open', { precision: 18, scale: 8 }).notNull(),
  high: decimal('high', { precision: 18, scale: 8 }).notNull(),
  low: decimal('low', { precision: 18, scale: 8 }).notNull(),
  close: decimal('close', { precision: 18, scale: 8 }).notNull(),
  volume: decimal('volume', { precision: 18, scale: 8 }).notNull(),
}, (table) => {
  return {
    symbolIntervalMarketTimeIdx: index('symbol_interval_market_time_idx').on(table.symbol, table.interval, table.marketType, table.openTime),
    unq: unique('candles_unq').on(table.symbol, table.interval, table.marketType, table.openTime),
  };
});

export const tickers24h = pgTable('tickers_24h', {
  id: serial('id').primaryKey(),
  symbol: varchar('symbol', { length: 32 }).notNull(),
  marketType: varchar('market_type', { length: 32 }).notNull(),
  lastPrice: decimal('last_price', { precision: 18, scale: 8 }),
  priceChange: decimal('price_change', { precision: 18, scale: 8 }),
  priceChangePercent: decimal('price_change_percent', { precision: 18, scale: 4 }),
  highPrice: decimal('high_price', { precision: 18, scale: 8 }),
  lowPrice: decimal('low_price', { precision: 18, scale: 8 }),
  volume: decimal('volume', { precision: 18, scale: 8 }),
  quoteVolume: decimal('quote_volume', { precision: 18, scale: 2 }),
  openTime: timestamp('open_time', { withTimezone: true }),
  closeTime: timestamp('close_time', { withTimezone: true }),
  count: integer('count'),
  lastUpdated: timestamp('last_updated', { withTimezone: true }).defaultNow().notNull(),
}, (table) => {
  return {
    marketTypeIdx: index('market_type_idx').on(table.marketType),
    quoteVolumeIdx: index('quote_volume_idx').on(table.quoteVolume),
    priceChangePercentIdx: index('price_change_percent_idx').on(table.priceChangePercent),
    unq: unique('tickers_24h_unq').on(table.symbol, table.marketType),
  };
}); 