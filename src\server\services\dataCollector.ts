import { pino } from 'pino';
import { binanceService } from './binance.service.js';
import { saveTickers, processTickerUpdate } from './tickerService.js';
import { MarketType } from '@/shared/types';
import { Kline, FullTicker } from '@/shared/schemas/market.schema';
import { exchangeDataCollector } from './exchangeDataCollectorService.js';

// Configure logger
const logger = pino({ name: 'data-collector', level: 'info' });

// Intervals for data updates
const UPDATE_INTERVALS = {
  symbols: 60 * 60 * 1000, // 1 hour
  tickers: 5 * 60 * 1000,  // 5 minutes
};

let symbolsUpdateInterval: NodeJS.Timeout | null = null;
let tickersUpdateInterval: NodeJS.Timeout | null = null;
let klineSubscriptions: { unsubscribe: () => void }[] = [];
let tickerSubscriptions: { unsubscribe: () => void }[] = [];

// Symbols we want to subscribe to for real-time updates
const WATCHED_SYMBOLS = [
  'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT', 
  'DOGEUSDT', 'XRPUSDT', 'DOTUSDT', 'UNIUSDT', 'LTCUSDT'
];

// Intervals we want to track
const WATCHED_INTERVALS = ['1m', '5m', '15m', '1h', '4h', '1d'];

/**
 * Initialize data collection from exchanges
 */
export async function initializeDataCollection(): Promise<void> {
  try {
    logger.info('Initializing data collection from exchanges...');
    
    // Initialize binance service
    await binanceService.initialize();
    
    // Fetch initial data for all markets
    await fetchInitialData();
    
    // Set up real-time subscriptions
    await subscribeToRealTimeData();
    
    // Set up regular polling for all symbols and tickers
    setupPeriodicUpdates();
    
    logger.info('Data collection initialized successfully.');
  } catch (error) {
    logger.error({ error }, 'Failed to initialize data collection');
    throw error;
  }
}

/**
 * Fetch initial data from exchanges
 */
async function fetchInitialData(): Promise<void> {
  try {
    logger.info('Fetching initial data...');
    
    // Fetch tickers for both spot and futures markets
    await fetchAllTickers();
    
    logger.info('Initial data fetch completed.');
  } catch (error) {
    logger.error({ error }, 'Failed to fetch initial data');
    throw error;
  }
}

/**
 * Set up periodic updates for all data
 */
function setupPeriodicUpdates(): void {
  logger.info('Setting up periodic updates...');
  
  // Schedule regular updates for symbols
  symbolsUpdateInterval = setInterval(async () => {
    try {
      await fetchAllSymbols();
    } catch (err) {
      logger.error({ err }, 'Error updating symbols');
    }
  }, UPDATE_INTERVALS.symbols);
  
  // Schedule regular updates for tickers
  tickersUpdateInterval = setInterval(async () => {
    try {
      await fetchAllTickers();
    } catch (err) {
      logger.error({ err }, 'Error updating tickers');
    }
  }, UPDATE_INTERVALS.tickers);
  
  logger.info('Periodic updates scheduled.');
}

/**
 * Fetch all symbols from exchanges
 */
async function fetchAllSymbols(): Promise<void> {
  try {
    logger.info('Fetching all symbols...');
    
    // Fetch symbols from spot and futures markets
    const spotSymbols = await binanceService.fetchSymbols('spot');
    const futuresSymbols = await binanceService.fetchSymbols('futures');
    
    // Store symbols in database or in-memory cache if needed
    
    logger.info(`Fetched ${spotSymbols.length} spot symbols and ${futuresSymbols.length} futures symbols.`);
  } catch (error) {
    logger.error({ error }, 'Failed to fetch symbols');
    throw error;
  }
}

/**
 * Fetch tickers for all markets
 */
async function fetchAllTickers(): Promise<void> {
  try {
    logger.info('Fetching all tickers...');
    
    // Fetch tickers from spot and futures markets
    const spotTickers = await binanceService.fetchTickers('spot');
    const futuresTickers = await binanceService.fetchTickers('futures');
    
    // Save all tickers to database
    await saveTickers([...spotTickers, ...futuresTickers]);
    
    logger.info(`Fetched ${spotTickers.length} spot tickers and ${futuresTickers.length} futures tickers.`);
  } catch (error) {
    logger.error({ error }, 'Failed to fetch tickers');
    throw error;
  }
}

/**
 * Subscribe to real-time data updates
 */
async function subscribeToRealTimeData(): Promise<void> {
  try {
    logger.info('Setting up real-time data subscriptions...');
    
    // Subscribe to ticker updates for both markets
    const spotTickerSub = binanceService.subscribeTickers('spot');
    const futuresTickerSub = binanceService.subscribeTickers('futures');
    
    tickerSubscriptions.push({ unsubscribe: spotTickerSub });
    tickerSubscriptions.push({ unsubscribe: futuresTickerSub });
    
    // Handle ticker events
    binanceService.on('ticker', (event) => {
      // Process each ticker in the event
      event.data.forEach((ticker: FullTicker) => processTickerUpdate(ticker));
    });
    
    // Subscribe to kline updates for watched symbols
    await subscribeToKlines();
    
    logger.info('Real-time data subscriptions established.');
  } catch (error) {
    logger.error({ error }, 'Failed to set up real-time data subscriptions');
    throw error;
  }
}

/**
 * Subscribe to kline/candlestick updates for watched symbols using batch subscriptions
 */
async function subscribeToKlines(): Promise<void> {
  // Clear any existing kline subscriptions
  unsubscribeAllKlines();

  logger.info('Subscribing to kline updates for watched symbols using batch subscriptions...');

  // Create symbol-interval pairs for batch subscription
  const symbolIntervalPairs = [];
  for (const symbol of WATCHED_SYMBOLS) {
    for (const interval of WATCHED_INTERVALS) {
      symbolIntervalPairs.push({ symbol, interval });
    }
  }

  // Subscribe to each market type using batch subscriptions
  for (const marketType of ['spot', 'futures'] as MarketType[]) {
    // Use only exchangeDataCollector with batch subscription for efficiency
    // This handles both data processing and DB storage
    await exchangeDataCollector.subscribeKlinesBatch(marketType, symbolIntervalPairs);

    logger.info(
      { marketType, symbolCount: WATCHED_SYMBOLS.length, intervalCount: WATCHED_INTERVALS.length },
      `Batch subscribed to ${symbolIntervalPairs.length} kline streams`
    );
  }

  // Handle kline events from Binance service
  binanceService.on('kline', (event) => {
    // Process kline event if needed
  });

  logger.info(`Subscribed to kline updates for ${WATCHED_SYMBOLS.length} symbols and ${WATCHED_INTERVALS.length} intervals.`);
}

/**
 * Unsubscribe from all kline updates
 */
function unsubscribeAllKlines(): void {
  if (klineSubscriptions.length > 0) {
    logger.info(`Unsubscribing from ${klineSubscriptions.length} kline streams...`);
    
    klineSubscriptions.forEach(sub => sub.unsubscribe());
    klineSubscriptions = [];
    
    logger.info('Unsubscribed from all kline streams.');
  }
}

/**
 * Shutdown data collection
 */
export async function shutdownDataCollection(): Promise<void> {
  try {
    logger.info('Shutting down data collection...');
    
    // Clear all intervals
    if (symbolsUpdateInterval) clearInterval(symbolsUpdateInterval);
    if (tickersUpdateInterval) clearInterval(tickersUpdateInterval);
    
    // Unsubscribe from real-time updates
    unsubscribeAllKlines();
    
    // Unsubscribe from ticker updates
    tickerSubscriptions.forEach(sub => sub.unsubscribe());
    tickerSubscriptions = [];
    
    // Shutdown binance service
    await binanceService.shutdown();
    
    logger.info('Data collection shutdown complete.');
  } catch (error) {
    logger.error({ error }, 'Error during data collection shutdown');
    throw error;
  }
} 