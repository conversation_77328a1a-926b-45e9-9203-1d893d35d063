import { z } from 'zod';

/**
 * -----------------------------------------------
 * Enums and Basic Types
 * -----------------------------------------------
 * This section defines fundamental enumerations and simple types used across the application's market data models.
 * These schemas provide a consistent and validated foundation for more complex data structures.
 */

/**
 * Enum for market types (e.g., spot or futures).
 * This is exported as a TypeScript enum to be used in other Zod schemas like `z.nativeEnum`.
 */
export enum MarketType {
  Spot = 'spot',
  Futures = 'futures'
}
export const MarketTypeSchema = z.nativeEnum(MarketType);

/**
 * Schema for kline/candlestick time intervals.
 * Defines the set of supported timeframes for chart data.
 */
export const KlineIntervalSchema = z.enum([
  '1m', '3m', '5m', '15m', '30m',
  '1h', '2h', '4h', '6h', '8h', '12h',
  '1d', '3d', '1w', '1M'
]);
export type KlineInterval = z.infer<typeof KlineIntervalSchema>;

/**
 * Schema for supported exchange identifiers.
 * This list can be expanded as more exchanges are integrated.
 */
export const ExchangeNameSchema = z.enum(['binance', 'kucoin', 'bybit', 'okx', 'huobi', 'custom']);
export type ExchangeName = z.infer<typeof ExchangeNameSchema>;

/**
 * Enum for WebSocket connection statuses.
 * Provides a clear, readable set of states for managing WebSocket connections.
 */
export enum WSConnectionStatus {
  Disconnected = "disconnected",
  Connecting = "connecting",
  Connected = "connected",
  Reconnecting = "reconnecting",
  Error = "error"
}
export const WSConnectionStatusSchema = z.nativeEnum(WSConnectionStatus);
export type WSConnectionStatusZod = z.infer<typeof WSConnectionStatusSchema>;

/**
 * Schema for UI View Modes, such as focused view or market screener.
 */
export const ViewModeSchema = z.enum(['focus', 'screener']);
export type ViewMode = z.infer<typeof ViewModeSchema>;


/**
 * -----------------------------------------------
 * Exchange-Specific Raw Data Schemas
 * -----------------------------------------------
 * Schemas in this section are designed to be flexible, accommodating raw data payloads from various external exchange APIs.
 * They serve as the initial validation layer before data is transformed into the core application format.
 */

/**
 * Schema for raw ticker data from any exchange.
 * Uses a record with `any` to accept diverse and unpredictable structures.
 */
export const ExchangeTickerDataSchema = z.record(z.any());
export type ExchangeTickerData = z.infer<typeof ExchangeTickerDataSchema>;

/**
 * Schema for raw kline/candlestick data from any exchange.
 */
export const ExchangeKlineDataSchema = z.record(z.any());
export type ExchangeKlineData = z.infer<typeof ExchangeKlineDataSchema>;

/**
 * Schema for raw symbol data from any exchange.
 */
export const ExchangeSymbolDataSchema = z.record(z.any());
export type ExchangeSymbolData = z.infer<typeof ExchangeSymbolDataSchema>;


/**
 * -----------------------------------------------
 * Internal Core Data Schemas
 * -----------------------------------------------
 * These schemas define the standardized, strictly-typed data structures used throughout the application's core logic.
 * Data from exchanges is transformed into these formats for consistent processing and usage.
 */

/**
 * Schema for core ticker data, representing a standardized ticker object.
 */
export const CoreTickerSchema = z.object({
  symbol: z.string(),
  marketType: MarketTypeSchema,
  price: z.number(),
  timestamp: z.number(),
  change: z.number().optional(),
  changePercent: z.number().optional(),
  volume: z.number().optional(),
  quoteVolume: z.number().optional(),
  high: z.number().optional(),
  low: z.number().optional(),
  count: z.number().optional(),
  open: z.number().optional(),
  weightedAvgPrice: z.number().optional(),
  prevClose: z.number().optional(),
  bidPrice: z.number().optional(),
  bidQty: z.number().optional(),
  askPrice: z.number().optional(),
  askQty: z.number().optional(),
  firstTradeId: z.number().optional(),
  lastTradeId: z.number().optional(),
});
export type CoreTicker = z.infer<typeof CoreTickerSchema>;

/**
 * Schema for core kline/candlestick data, standardized for internal use.
 */
export const CoreKlineSchema = z.object({
  symbol: z.string(),
  marketType: MarketTypeSchema,
  interval: z.string(), // String, as it can be any timeframe before validation against KlineIntervalSchema
  openTime: z.number(),
  open: z.number(),
  high: z.number(),
  low: z.number(),
  close: z.number(),
  volume: z.number(),
  closeTime: z.number(),
  quoteVolume: z.number().optional(),
  trades: z.number().optional(),
  isClosed: z.boolean().optional(),
});
export type CoreKline = z.infer<typeof CoreKlineSchema>;

/**
 * Schema for core symbol information, detailing asset properties.
 */
export const CoreSymbolSchema = z.object({
  symbol: z.string(),
  marketType: MarketTypeSchema,
  baseAsset: z.string(),
  quoteAsset: z.string(),
  status: z.string().optional(),
  pricePrecision: z.number().optional(),
  quantityPrecision: z.number().optional(),
  isSpotTradingAllowed: z.boolean().optional(),
  isMarginTradingAllowed: z.boolean().optional(),
});
export type CoreSymbol = z.infer<typeof CoreSymbolSchema>;


/**
 * -----------------------------------------------
 * Application-Specific and DTO Schemas
 * -----------------------------------------------
 * This section contains schemas for data transfer objects (DTOs) and other application-specific structures,
 * such as data formatted for UI components, database interaction, or API parameters.
 */

/**
 * Schema for candlestick/kline data from the database.
 * Historical data might have numeric fields stored as strings.
 */
export const KlineDbSchema = z.object({
  openTime: z.date(),
  open: z.string(),
  high: z.string(),
  low: z.string(),
  close: z.string(),
  volume: z.string(),
});
export type KlineDb = z.infer<typeof KlineDbSchema>;

/**
 * Schema for the primary kline/candlestick data representation used in business logic and UI.
 */
export const KlineSchema = z.object({
  symbol: z.string(),
  marketType: MarketTypeSchema,
  interval: KlineIntervalSchema,
  openTime: z.number(),
  open: z.number(),
  high: z.number(),
  low: z.number(),
  close: z.number(),
  volume: z.number(),
  closeTime: z.number(),
  quoteVolume: z.number(),
  trades: z.number(),
  isClosed: z.boolean().default(true)
});
export type Kline = z.infer<typeof KlineSchema>;

/**
 * Schema for parameters used when fetching kline data.
 */
export const KlineParamsSchema = z.object({
  symbol: z.string(),
  interval: z.string(),
  limit: z.number().optional(),
});
export type KlineParams = z.infer<typeof KlineParamsSchema>;

/**
 * Schema for full 24-hour ticker statistics.
 * This is the comprehensive ticker model.
 */
export const TickerSchema = z.object({
  symbol: z.string(),
  marketType: MarketTypeSchema,
  lastPrice: z.number(),
  priceChange: z.number(),
  priceChangePercent: z.number(),
  weightedAvgPrice: z.number().optional().default(0),
  prevClosePrice: z.number().optional().default(0),
  lastQty: z.number().optional().default(0),
  bidPrice: z.number().optional().default(0),
  bidQty: z.number().optional().default(0),
  askPrice: z.number().optional().default(0),
  askQty: z.number().optional().default(0),
  openPrice: z.number().optional().default(0),
  highPrice: z.number(),
  lowPrice: z.number(),
  volume: z.number(),
  quoteVolume: z.number(),
  openTime: z.number(),
  closeTime: z.number(),
  firstId: z.number(),
  lastId: z.number(),
  count: z.number(),
  lastUpdated: z.number(),
});
export type Ticker = z.infer<typeof TickerSchema>;

/**
 * Schema for a simplified ticker object, often used in high-level UI lists.
 * It picks essential fields and adds a 'price' alias for convenience.
 */
export const SimpleTickerSchema = TickerSchema.pick({
  symbol: true,
  marketType: true,
  lastPrice: true,
  priceChangePercent: true,
  volume: true,
  quoteVolume: true,
  count: true,
  lastUpdated: true,
}).transform(val => ({ ...val, price: val.lastPrice }));
export type SimpleTicker = z.infer<typeof SimpleTickerSchema>;


/**
 * Schema for a processed ticker with additional calculated fields, like USD volume.
 */
export const ProcessedTickerSchema = TickerSchema.extend({
  baseAsset: z.string(),
  quoteAsset: z.string(),
  priceChangeAbs: z.number(),
  volumeInUSD: z.number().optional(),
  aggregatedVolume: z.number().optional(),
  aggregatedTradeCount: z.number().optional(),
});
export type ProcessedTicker = z.infer<typeof ProcessedTickerSchema>;

/**
 * Schema for detailed symbol information used within the application.
 */
export const AppSymbolInfoSchema = z.object({
  symbol: z.string(),
  status: z.string(),
  baseAsset: z.string(),
  quoteAsset: z.string(),
  baseAssetPrecision: z.number(),
  quoteAssetPrecision: z.number(),
  marketType: MarketTypeSchema,
});
export type AppSymbolInfo = z.infer<typeof AppSymbolInfoSchema>;

/**
 * Schema for kline data specifically formatted for use in technical indicators.
 */
export const KlineDataSchema = z.object({
  time: z.number(),
  open: z.number(),
  high: z.number(),
  low: z.number(),
  close: z.number(),
  volume: z.number(),
});
export type KlineData = z.infer<typeof KlineDataSchema>;

/**
 * Schema for TradingView-compatible symbol information.
 */
export const TradingViewSymbolSchema = z.object({
  symbol: z.string(),
  full_name: z.string(),
  description: z.string(),
  exchange: z.string(),
  type: z.string(),
});
export type TradingViewSymbol = z.infer<typeof TradingViewSymbolSchema>;


/**
 * -----------------------------------------------
 * Charting Constants and Enums
 * -----------------------------------------------
 * This section defines constants and enumerations related to chart events and interactions.
 */

export const CHART_EVENT_NAMESPACE = 'chart';

export enum ChartEventType {
  CROSSHAIR_MOVE = 'crosshair-move',
  VISIBLE_RANGE_CHANGE = 'visible-range-change',
  TIME_SCALE_RESET = 'time-scale-reset',
}
export const ChartEventTypeSchema = z.nativeEnum(ChartEventType);


